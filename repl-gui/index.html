<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kashf REPL - Interactive Code Environment</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/hint/show-hint.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldgutter.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/dialog/dialog.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            color: white;
            overflow-y: auto;
            transition: transform 0.3s ease, width 0.3s ease;
            flex-shrink: 0;
        }

        .sidebar.hidden {
            transform: translateX(-100%);
            width: 0;
            padding: 0;
            border-right: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: none;
        }

        .sidebar-toggle:hover {
            background: rgba(0, 0, 0, 0.7);
        }

        .sidebar-toggle.show {
            display: block;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: white;
            font-size: 24px;
            font-weight: 300;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #ff4757;
            animation: pulse 2s infinite;
        }

        .status-indicator.connected {
            background: #2ed573;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .editor-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            gap: 20px;
        }

        .panels-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
            height: calc(100vh - 180px); /* Account for header and controls */
            overflow: hidden;
        }

        .panels-container.vertical {
            display: grid !important;
            grid-template-columns: 1fr auto 1fr;
            grid-template-rows: 1fr;
            gap: 0;
            height: 100%;
        }

        .panels-container.horizontal {
            flex-direction: column;
            gap: 20px;
        }

        .panels-container.horizontal .editor-panel,
        .panels-container.horizontal .output-panel {
            flex: 1;
            height: 50%;
            min-height: 200px;
        }

        .panels-container.horizontal .code-section,
        .panels-container.horizontal .result-container {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .panels-container.horizontal .CodeMirror {
            flex: 1;
            height: auto;
            overflow: auto !important;
        }

        .editor-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 300px;
            min-height: 200px;
        }

        .output-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 300px;
            min-height: 200px;
        }

        .panels-container.vertical .editor-panel {
            grid-column: 1;
            grid-row: 1;
            height: 100%;
            min-width: 300px;
        }

        .panels-container.vertical .output-panel {
            grid-column: 3;
            grid-row: 1;
            height: 100%;
            min-width: 300px;
        }

        .panels-container.vertical .editor-panel,
        .panels-container.vertical .output-panel {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .panels-container.vertical .editor-panel .code-section,
        .panels-container.vertical .output-panel .result-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            flex: 1;
        }

        .panels-container.vertical .result-content {
            flex: 1;
            overflow-y: auto;
            height: 100%;
        }

        .panels-container.vertical .editor-panel .code-section .CodeMirror,
        .panels-container.vertical .output-panel .result-content {
            flex: 1;
            height: auto;
            min-height: 0; /* Allow flex to shrink */
        }

        .panels-container.vertical .CodeMirror {
            height: 100% !important;
            flex: 1;
        }

        .panels-container.vertical .code-section {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .panels-container.vertical .section-header {
            flex-shrink: 0;
        }



        .resizer {
            background: rgba(255, 255, 255, 0.1);
            cursor: col-resize;
            width: 4px;
            position: relative;
            transition: background 0.2s ease;
            flex-shrink: 0;
            z-index: 10;
        }

        .resizer:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .resizer.horizontal {
            cursor: row-resize;
            height: 4px;
            width: 100%;
        }

        .panels-container.vertical .resizer {
            grid-column: 2;
            grid-row: 1;
            width: 4px;
            height: 100%;
            cursor: col-resize;
        }

        .resizer:active {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Prevent text selection during resize */
        .resizing {
            user-select: none;
            pointer-events: none;
        }

        .resizing * {
            user-select: none;
            pointer-events: none;
        }

        /* Ensure panels maintain their minimum sizes */
        .editor-panel .code-section {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .editor-panel .CodeMirror {
            flex: 1;
            height: auto;
            min-height: 200px;
            overflow: auto !important;
        }

        .output-panel .result-container {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .output-panel .result-content {
            flex: 1;
            overflow-y: auto;
            overflow-x: auto;
            min-height: 200px;
            max-height: 100%;
        }

        .layout-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .layout-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }

        .layout-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .layout-btn.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-color: transparent;
        }

        .code-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-header {
            background: rgba(0, 0, 0, 0.2);
            padding: 10px 15px;
            color: white;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .CodeMirror {
            height: 100%;
            font-family: 'Fira Code', 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            overflow: auto !important;
        }

        .CodeMirror-scroll {
            overflow: auto !important;
            max-height: 100%;
        }

        .result-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .result-content {
            padding: 15px;
            color: white;
            font-family: 'Fira Code', 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            flex: 1;
            overflow-y: auto;
            overflow-x: auto;
            max-height: 100%;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .result-success {
            border-left: 4px solid #2ed573;
        }

        .result-error {
            border-left: 4px solid #ff4757;
        }

        .result-output {
            border-left: 4px solid #ffa502;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .sidebar h3 {
            margin-bottom: 15px;
            color: #ffa502;
        }

        .sidebar ul {
            list-style: none;
            margin-bottom: 20px;
        }

        .sidebar li {
            padding: 5px 0;
            font-size: 14px;
            opacity: 0.8;
        }

        .examples {
            margin-top: 20px;
        }

        .example-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
            font-size: 12px;
        }

        .example-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .loading {
            display: none;
            color: #ffa502;
        }

        .loading.show {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 165, 2, 0.3);
            border-top: 2px solid #ffa502;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .sidebar {
                width: 250px;
            }

            .controls {
                flex-wrap: wrap;
                gap: 8px;
            }

            .btn {
                padding: 6px 12px;
                font-size: 12px;
            }

            .layout-btn {
                padding: 6px 10px;
                font-size: 11px;
            }
        }

        @media (max-width: 480px) {
            .sidebar {
                width: 200px;
            }

            .editor-panel, .output-panel {
                min-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <button class="sidebar-toggle" id="sidebar-toggle" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>
        <div class="sidebar" id="sidebar">
            <h3><i class="fas fa-info-circle"></i> Context Info</h3>
            <div id="context-info">
                <p>Loading context...</p>
            </div>

            <h3><i class="fas fa-code"></i> Examples</h3>
            <div class="examples">
                <div class="example-item" onclick="loadExample('help')">
                    <strong>help()</strong><br>
                    Show available commands
                </div>
                <div class="example-item" onclick="loadExample('basic')">
                    <strong>Basic JavaScript</strong><br>
                    console.log('Hello World!')
                </div>
                <div class="example-item" onclick="loadExample('async')">
                    <strong>Async Operations</strong><br>
                    await Promise.resolve('Done!')
                </div>
                <div class="example-item" onclick="loadExample('nestjs')">
                    <strong>NestJS Service</strong><br>
                    const service = getService(UsersService)
                </div>
            </div>

            <h3><i class="fas fa-keyboard"></i> Shortcuts</h3>
            <ul>
                <li><strong>Ctrl+Enter:</strong> Execute code</li>
                <li><strong>Ctrl+/:</strong> Toggle comment</li>
                <li><strong>Ctrl+L:</strong> Clear results</li>
                <li><strong>Ctrl+K:</strong> Clear editor</li>
                <li><strong>Ctrl+D:</strong> Duplicate line</li>
                <li><strong>Ctrl+F:</strong> Find</li>
                <li><strong>Ctrl+H:</strong> Find & replace</li>
                <li><strong>Tab:</strong> Auto-complete</li>
                <li><strong>Ctrl+Space:</strong> Force autocomplete</li>
                <li><strong>Alt+Up/Down:</strong> Move line up/down</li>
            </ul>
        </div>

        <div class="main-content">
            <div class="header">
                <h1><i class="fas fa-terminal"></i> Kashf REPL</h1>
                <div style="display: flex; align-items: center; gap: 20px;">
                    <div class="layout-controls">
                        <span style="font-size: 12px; opacity: 0.8;">Layout:</span>
                        <button class="layout-btn active" id="horizontal-btn" onclick="setLayout('horizontal')">
                            <i class="fas fa-grip-lines"></i> Horizontal
                        </button>
                        <button class="layout-btn" id="vertical-btn" onclick="setLayout('vertical')">
                            <i class="fas fa-grip-lines-vertical"></i> Vertical
                        </button>
                    </div>
                    <div class="status">
                        <div class="status-indicator" id="connection-status"></div>
                        <span id="status-text">Connecting...</span>
                    </div>
                </div>
            </div>

            <div class="editor-container" id="editor-container">
                <div class="controls">
                    <button class="btn btn-primary" onclick="executeCode()" id="execute-btn">
                        <i class="fas fa-play"></i> Execute
                    </button>
                    <button class="btn btn-secondary" onclick="clearResults()">
                        <i class="fas fa-trash"></i> Clear Results
                    </button>
                    <button class="btn btn-secondary" onclick="clearEditor()">
                        <i class="fas fa-eraser"></i> Clear Editor
                    </button>
                    <button class="btn btn-secondary" onclick="loadSavedCode()" title="Load previously saved code">
                        <i class="fas fa-folder-open"></i> Load Saved
                    </button>
                    <button class="btn btn-secondary" onclick="formatCode()" title="Format/beautify code">
                        <i class="fas fa-magic"></i> Format
                    </button>
                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        Executing...
                    </div>
                </div>

                <div class="panels-container horizontal" id="panels-container">
                    <div class="editor-panel">
                    <div class="code-section">
                        <div class="section-header">
                            <span><i class="fas fa-edit"></i> Code Editor</span>
                            <div style="display: flex; gap: 15px; align-items: center;">
                                <span id="cursor-position" style="font-size: 12px; opacity: 0.7;">Line 1, Column 1</span>
                                <span style="font-size: 12px;">Press Ctrl+Enter to execute</span>
                            </div>
                        </div>
                        <textarea id="code-editor">// Welcome to Kashf REPL!
// This is a Tinkerwell-like environment for your NestJS application
// Type JavaScript/TypeScript code and press Ctrl+Enter to execute

// Start with the help command to see available functions
help()</textarea>
                    </div>
                </div>

                <div class="resizer" id="resizer"></div>

                <div class="output-panel">
                    <div class="result-container">
                        <div class="section-header">
                            <span><i class="fas fa-terminal"></i> Results</span>
                            <span id="execution-time"></span>
                        </div>
                        <div class="result-content" id="results">
                            <div style="opacity: 0.7;">Results will appear here...</div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/hint/show-hint.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/hint/javascript-hint.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/comment/comment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/selection/active-line.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/trailingspace.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldgutter.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/brace-fold.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/comment-fold.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/search/search.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/search/searchcursor.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/dialog/dialog.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="app.js"></script>
</body>
</html>
